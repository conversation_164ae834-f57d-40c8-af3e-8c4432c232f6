import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { AIModelsSelector } from '@bika/domains/ai/client/ai-models/ai-models-selector/ai-models-selector';
import React, { useState } from 'react';
import { Box } from '@bika/ui/layouts';
import type { IAIModelSelectBO } from '@bika/types/ai/bo';

// Mock data for stories
const mockAutoValue: IAIModelSelectBO = {
  kind: 'auto',
};

const mockPresetValue: IAIModelSelectBO = {
  kind: 'preset',
  model: 'gpt-4o-mini',
};

const mockCustomValue: IAIModelSelectBO = {
  kind: 'custom',
  custom: {
    type: 'manual',
    provider: {
      type: 'OPENAI',
      apiKey: 'sk-test-key-123',
      baseUrl: 'https://api.openai.com/v1',
    },
    modelId: 'gpt-3.5',
  },
};

const mockCustomIntegrationValue: IAIModelSelectBO = {
  kind: 'custom',
  custom: {
    type: 'integration',
    integrationId: 'integration-123',
    modelId: 'gpt-4o',
  },
};

// Example wrapper component to manage state
function AIModelsSelectorExample(props: {
  initialValue?: IAIModelSelectBO;
  disabled?: boolean;
}) {
  const [value, setValue] = useState<IAIModelSelectBO>(
    props.initialValue || mockAutoValue
  );

  const handleChange = (newValue: IAIModelSelectBO) => {
    setValue(newValue);
    console.log('AI Model changed:', newValue);
  };

  return (
    <Box sx={{ width: '100%', maxWidth: '600px', margin: '0 auto' }}>
      <AIModelsSelector
        value={value}
        onChange={handleChange}
      />
      
      {/* Debug info */}
      <Box
        sx={{
          marginTop: 3,
          padding: 2,
          background: 'var(--bg-elevated)',
          border: '1px solid var(--border-default)',
          borderRadius: '8px',
          fontSize: '12px',
          fontFamily: 'monospace',
        }}
      >
        <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', fontFamily: 'inherit' }}>
          Current Value:
        </h4>
        <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(value, null, 2)}
        </pre>
      </Box>
    </Box>
  );
}

const meta = {
  title: '@bika/ai/AIModelsSelector',
  component: AIModelsSelectorExample,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'AI Models Selector component for choosing between auto, preset, and custom AI model configurations.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    initialValue: {
      control: 'object',
      description: 'Initial AI model selection value',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the selector is disabled',
    },
  },
  args: {
    disabled: false,
  },
} satisfies Meta<typeof AIModelsSelectorExample>;

export default meta;
type Story = StoryObj<typeof AIModelsSelectorExample>;

// Default story with auto selection
export const Default: Story = {
  args: {
    initialValue: mockAutoValue,
  },
};

// Story with preset model selected
export const PresetModel: Story = {
  args: {
    initialValue: mockPresetValue,
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the selector with a preset AI model (GPT-4o Mini) selected.',
      },
    },
  },
};

// Story with custom manual configuration
export const CustomManual: Story = {
  args: {
    initialValue: mockCustomValue,
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the selector with custom manual AI model configuration, including API key and base URL fields.',
      },
    },
  },
};

// Story with custom integration configuration
export const CustomIntegration: Story = {
  args: {
    initialValue: mockCustomIntegrationValue,
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the selector with custom integration-based AI model configuration.',
      },
    },
  },
};
